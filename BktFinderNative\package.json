{"name": "doraepare", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-community/geolocation": "^3.2.1", "react": "^19.1.0", "react-native": "0.80.1", "react-native-dotenv": "^3.4.11", "react-native-loading-spinner-overlay": "^3.0.1", "react-native-webview": "^13.12.2"}, "devDependencies": {"@babel/core": "^7.25.9", "@babel/preset-env": "^7.25.9", "@babel/runtime": "^7.25.9", "@react-native-community/cli": "^15.1.2", "@react-native-community/eslint-config": "^3.2.0", "@tsconfig/react-native": "^3.0.5", "@types/jest": "^29.5.13", "@types/node": "^20.17.10", "@types/react": "^19.1.0", "@types/react-test-renderer": "^19.1.0", "babel-jest": "^29.7.0", "eslint": "^8.57.1", "jest": "^29.7.0", "metro-react-native-babel-preset": "^0.77.0", "prettier": "^3.4.2", "react-test-renderer": "^19.1.0", "typescript": "^5.7.3"}, "jest": {"preset": "react-native"}}